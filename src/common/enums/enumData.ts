export const enumData = {
  Page: {
    pageIndex: 1,
    pageSize: 10,
    pageSizeMax: 1000000,
    total: 0,
    lstPageSize: [10, 20, 30, 40, 50, 100]
  },
  FILTER_TYPE: {
    INPUT: { key: 'input', description: 'String Input' },
    INPUT_NUMBER: { key: 'inputNumber', description: 'Number Input' },
    SELECT: { key: 'select', description: 'Select Input' },
    SELECT_SEARCH: { key: 'selectSearch', description: 'Select Input Allow Search' },
    DATE: { key: 'date', description: 'DatePicker' },
    DATE_RANGE: { key: 'dateRange', description: 'Date Picker Range' }
  },

  MEMBER_ACTIVE_STATUS: {
    ACTIVE: { key: 'ACTIVE', value: 'Đang hoạt động', description: 'Active', color: 'green' },
    // INACTIVE: { key: 'INACTIVE', value: 'Ngưng hoạt động', description: 'Inactive', color: 'red' },
    LOCKED: { key: 'LOCKED', value: 'Tài khoản bị khóa', description: 'Locked', color: 'orange' }
  },
  MEMBER_VALIDATE_STATUS: {
    VALIDATED: { key: 'VALIDATED', value: 'Đã xác thực', description: 'Validated', color: 'blue' },
    INVALIDATE: { key: 'INVALIDATE', value: 'Chưa xác thực', description: 'INVALIDATE', color: 'red' }
  },

  NEWS_STATUS: {
    DRAFT: { key: 'DRAFT', value: 'Bản nháp', description: 'Draft', color: 'gray' },
    PUBLISHED: { key: 'PUBLISHED', value: 'Đã đăng', description: 'Published', color: 'green' },
    // ARCHIVED: { key: 'ARCHIVED', value: 'Đã lưu trữ', description: 'Archived', color: 'blue' },
    INACTIVE: { key: 'INACTIVE', value: 'Không hoạt động', description: 'Inactive', color: 'red' }
  },

  DURATION_TYPE: {
    MONTHLY: { key: 'MONTHLY', value: 'Tháng', description: 'Theo tháng' },
    YEARLY: { key: 'YEARLY', value: 'Năm', description: 'Theo năm' }
  },

  PACKAGE_STATUS: {
    INACTIVE: { key: 'INACTIVE', value: 'Không hoạt động', description: 'Inactive', color: 'red' },
    ACTIVE: { key: 'ACTIVE', value: 'Hoạt động', description: 'Active', color: 'green' }
  },

  CONFIG_STATUS: {
    ACTIVE: { key: 'ACTIVE', value: 'Hoạt động', description: 'Active', color: 'green' },
    INACTIVE: { key: 'INACTIVE', value: 'Không hoạt động', description: 'Inactive', color: 'red' }
  },

  MEMBER_PACKAGE_STATUS: {
    ACTIVE: { key: 'ACTIVE', value: 'Đang hoạt động', description: 'Active', color: 'green' },
    EXPIRED: { key: 'EXPIRED', value: 'Hết hạn', description: 'Expired', color: 'red' },
    PENDING: { key: 'PENDING', value: 'Chờ kích hoạt', description: 'Pending', color: 'orange' },
    //CANCELLED
    CANCELED: { key: 'CANCELED', value: 'Đã hủy', description: 'Cancelled', color: 'red' }
  },

  // export enum ETransactionStatus {
  //       PENDING = 'PENDING', // Đang chờ xử lý: Giao dịch đã được tạo nhưng chưa hoàn tất hoặc chưa xác nhận
  //       COMPLETED = 'COMPLETED', // Hoàn tất: Giao dịch đã thành công và tiền đã được chuyển
  //       FAILED = 'FAILED', // Thất bại: Giao dịch không thành công do lỗi hệ thống, thẻ bị từ chối hoặc lỗi khác
  //       CANCELED = 'CANCELED', // Đã hủy: Giao dịch đã bị hủy bởi người dùng hoặc hệ thống trước khi hoàn tất
  //       REFUNDED = 'REFUNDED', // Đã hoàn tiền: Số tiền của giao dịch đã được hoàn lại cho người dùng
  //       EXPIRED = 'EXPIRED', // Hết hạn: Giao dịch không được hoàn tất trong thời gian quy định và tự động hết hiệu lực
  //       IN_PROGRESS = 'IN_PROGRESS', // Đang xử lý: Giao dịch đang được xử lý và có thể mất thời gian để hoàn tất
  //       ON_HOLD = 'ON_HOLD', // Tạm giữ: Giao dịch bị tạm dừng do các vấn đề cần xem xét thêm (như xác minh tài khoản)
  //   }

  TRANSACTION_STATUS: {
    //  DRAFT = 'DRAFT', // Nháp
    //     PENDING = 'PENDING', // Chờ thanh toán
    //     COMPLETED = 'COMPLETED',
    //     CANCELLED = 'CANCELLED',
    COMPLETED: { key: 'COMPLETED', value: 'Đã thanh toán', description: 'Đã thanh toán', color: 'green' },
    PENDING: { key: 'PENDING', value: 'Chưa thanh toán', description: 'Chưa thanh toán', color: 'orange' }
    // CANCELLED: { key: 'CANCELLED', value: 'Đã hủy', description: 'Đã hủy', color: 'red' },
    // DRAFT: { key: 'DRAFT', value: 'Nháp', description: 'Nháp', color: 'blue' }
  },

  ORDER_STATUS: {
    PENDING: { key: 'PENDING', value: 'Chờ xử lý', description: 'Chờ xử lý', color: 'orange' },
    COMPLETED: { key: 'COMPLETED', value: 'Hoàn thành', description: 'Hoàn thành', color: 'green' },
    CANCELLED: { key: 'CANCELLED', value: 'Đã hủy', description: 'Đã hủy', color: 'red' }
  },
  ORDER_PAYMENT_STATUS: {
    PAID: { key: 'PAID', value: 'Đã thanh toán', description: 'Đã thanh toán', color: 'green' },
    UNPAID: { key: 'UNPAID', value: 'Chưa thanh toán', description: 'Chưa thanh toán', color: 'orange' }
    // REFUNDED: { key: 'REFUNDED', value: 'Đã hoàn tiền', description: 'Đã hoàn tiền', color: 'geekblue' }
  },

  ADVISORY_STATUS: {
    // PENDING = 'PENDING',
    //     IN_PROGRESS = 'IN_PROGRESS',
    //     DONE = 'DONE',
    //     REJECTED = 'REJECTED',

    PENDING: { key: 'PENDING', value: 'Chờ xử lý', description: 'Chờ xử lý', color: 'orange' },
    DONE: { key: 'DONE', value: 'Đã xử lý', description: 'Đã xử lý', color: 'green' },
    REJECTED: { key: 'REJECTED', value: 'Từ chối', description: 'Từ chối', color: 'red' },
    IN_PROGRESS: { key: 'IN_PROGRESS', value: 'Đang xử lý', description: 'Đang xử lý', color: 'blue' }
  },

  TERMS_AND_PRIVACY_STATUS: {
    ACTIVE: { key: 'ACTIVE', value: 'Hoạt động', description: 'Hoạt động', color: 'green' },
    INACTIVE: { key: 'INACTIVE', value: 'Không hoạt động', description: 'Không hoạt động', color: 'red' }
  }
}
