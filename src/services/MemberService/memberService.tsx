import { environment } from '~/environments/environment'
import { rootApiService } from '../@common'
import { IMember, IMemberFilter, IMemberList } from '~/dto/member.dto'
import { IMemberConfigPackageResponse, IMemberSubscriptionPlan, IMemberSubscriptionPlanResponse, ISubscriptionPlan } from '~/dto/subscriptionPlan.dto'
import { IOrder, IOrderRespone } from '~/dto/order.dto'

class MemberService {
  constructor() {}
  backEnd = environment.backEnd
  MEMBER = {
    LIST: `${this.backEnd}/api/admin/member`,
    PACKAGES: `${this.backEnd}/api/admin/member/packages`,
    ORDERS: `${this.backEnd}/api/admin/member/orders`,
    CONFIG: `${this.backEnd}/api/admin/member/config-packages`,
    KEY: `${this.backEnd}/api/admin/member/member-key`,
    BLOCK: `${this.backEnd}/api/admin/member/block`,
    UNBLOCK: `${this.backEnd}/api/admin/member/unblock`
  }

  async getMemberList(filter: IMemberFilter): Promise<IMemberList> {
    return await rootApiService.get(this.MEMBER.LIST, filter)
  }

  async getMemberPackages(body: { id: string; pageSize?: number; pageIndex?: number }): Promise<IMemberSubscriptionPlanResponse> {
    return await rootApiService.post(this.MEMBER.PACKAGES, body)
  }

  async getMemberOrders(body: { id: string; pageSize?: number; pageIndex?: number }): Promise<IOrderRespone> {
    return await rootApiService.post(this.MEMBER.ORDERS, body)
  }

  async getMemberConfigPackages(body: { id: string; pageSize?: number; pageIndex?: number }): Promise<IMemberConfigPackageResponse> {
    return await rootApiService.post(this.MEMBER.CONFIG, body)
  }

  async getMemberKey(body: { id: string }) {
    return await rootApiService.post(this.MEMBER.KEY, body)
  }

  async blockMember(id: string) {
    return await rootApiService.post(this.MEMBER.BLOCK, { id })
  }

  async unblockMember(id: string) {
    return await rootApiService.post(this.MEMBER.UNBLOCK, { id })
  }
}

export const memberService = new MemberService()
