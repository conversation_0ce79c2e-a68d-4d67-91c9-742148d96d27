import React, { useState } from 'react'
import { BORDER_RADIUS, SIZE, SPACING } from '~/common/constants/size'
import { COLORS } from '~/common/constants/colors'

interface BaseCheckboxProps {
  checked: boolean
  onChange?: (checked: boolean) => void
  disabled?: boolean
  sizeCheckbox?: 'small' | 'medium' | 'large'
  sizeText?: 'small' | 'medium' | 'large'
  label?: React.ReactNode
}

const BaseCheckbox: React.FC<BaseCheckboxProps> = ({
  checked,
  onChange,
  disabled = false,
  sizeCheckbox = 'medium',
  sizeText = 'medium',
  label
}) => {
  const [isChecked, setIsChecked] = useState(checked)
  const [isHovered, setIsHovered] = useState(false)

  const handleChange = () => {
    if (!disabled) {
      const newChecked = !isChecked
      setIsChecked(newChecked)
      if (onChange) onChange(newChecked)
    }
  }

  const getCheckboxSize = () => {
    switch (sizeCheckbox) {
      case 'small':
        return SIZE.SMALL
      case 'large':
        return SIZE.LARGE
      default:
        return SIZE.MEDIUM
    }
  }

  return (
    <div
      style={{
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        width: '100%'
      }}>
      <div
        style={{
          width: getCheckboxSize(),
          height: getCheckboxSize(),
          borderRadius: BORDER_RADIUS.BR_2,
          backgroundColor: isChecked ? COLORS.PRIMARY_RGB : 'transparent',
          cursor: disabled ? 'not-allowed' : 'pointer',
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
          marginRight: SPACING.SP_2,
          transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
          transform: `scale(${isHovered && !disabled ? 1.05 : 1})`,
          boxShadow:
            isHovered && !disabled ? `0 0 0 3px ${COLORS.PRIMARY}20` : 'none'
        }}
        onClick={handleChange}
        onMouseEnter={() => setIsHovered(true)}
        onMouseLeave={() => setIsHovered(false)}>
        <div
          style={{
            width: getCheckboxSize() - 6,
            height: getCheckboxSize() - 6,
            backgroundColor: COLORS.WHITE,
            clipPath:
              'polygon(14% 44%, 0 65%, 50% 100%, 100% 16%, 80% 0%, 43% 62%)',
            transform: `scale(${isChecked ? 1 : 0})`,
            transition: 'transform 0.2s cubic-bezier(0.68, -0.55, 0.265, 1.55)',
            opacity: isChecked ? 1 : 0
          }}
        />
      </div>
      <p
        style={{
          fontSize: SIZE[sizeText],
          color: disabled ? COLORS.GRAY.MEDIUM : COLORS.BLACK,
          transition: 'color 0.2s ease'
        }}>
        {label}
      </p>
    </div>
  )
}

export default BaseCheckbox
