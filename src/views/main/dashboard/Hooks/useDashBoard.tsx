import { useState } from 'react'
import { dashBoardService } from '~/services/DashBoardService/dashBoardService'

export const useDashBoard = (enabled: boolean = false) => {
  const [isLoading, setIsLoading] = useState(false)
  const [packagesStats, setPackagesStats] = useState<any>({})
  const getPackagesStats = async (year: any) => {
    setIsLoading(true)
    await dashBoardService.getPackagesStats(year).then((res) => {
      setPackagesStats(res)
    })
    setIsLoading(false)
  }

  const [paymentGatewayStats, setPaymentGatewayStats] = useState<any>({})
  const getPaymentGatewayStats = async (year: any) => {
    setIsLoading(true)
    await dashBoardService.getPaymentGatewayStats(year).then((res) => {
      setPaymentGatewayStats(res)
    })
    setIsLoading(false)
  }

  const [revenueStats, setRevenueStats] = useState<any>({})
  const getRevenueStats = async (year: any) => {
    setIsLoading(true)
    await dashBoardService.getRevenueStats(year).then((res) => {
      setRevenueStats(res)
    })
    setIsLoading(false)
  }

  const [customerStats, setCustomerStats] = useState<any>({})
  const getCustomerStats = async (year: any) => {
    setIsLoading(true)
    await dashBoardService.getCustomerStats(year).then((res) => {
      setCustomerStats(res)
    })
    setIsLoading(false)
  }

  const [orderStats, setOrderStats] = useState<any>({})
  const getOrderStats = async (year: any) => {
    setIsLoading(true)
    await dashBoardService.getOrderStats(year).then((res) => {
      setOrderStats(res)
    })
    setIsLoading(false)
  }

  const [totalCustomer, setTotalCustomer] = useState<any>({})
  const getTotalCustomer = async () => {
    setIsLoading(true)
    await dashBoardService.getTotalCustomer().then((res) => {
      setTotalCustomer(res)
    })
    setIsLoading(false)
  }

  const [totalOrder, setTotalOrder] = useState<any>({})
  const getTotalOrder = async () => {
    setIsLoading(true)
    await dashBoardService.getTotalOrder().then((res) => {
      setTotalOrder(res)
    })
    setIsLoading(false)
  }

  const [totalRevenue, setTotalRevenue] = useState<any>({})
  const getTotalRevenue = async () => {
    setIsLoading(true)
    await dashBoardService.getTotalRevenue().then((res) => {
      setTotalRevenue(res)
    })
    setIsLoading(false)
  }

  return {
    packagesStats,
    getPackagesStats,
    paymentGatewayStats,
    getPaymentGatewayStats,
    revenueStats,
    getRevenueStats,
    customerStats,
    getCustomerStats,
    orderStats,
    getOrderStats,
    totalCustomer,
    getTotalCustomer,
    totalOrder,
    getTotalOrder,
    totalRevenue,
    getTotalRevenue,
    isLoading
  }
}
