import { Card, Tag } from 'antd'
import { ColumnsType } from 'antd/es/table'
import moment from 'moment'
import { enumData } from '~/common/enums/enumData'
import BaseTable from '~/components/BaseTable'
import { IOrder, IOrderRespone } from '~/dto/order.dto'
import { IMemberSubscriptionPlan, IMemberSubscriptionPlanResponse } from '~/dto/subscriptionPlan.dto'
import { useMember } from '../../Hooks/useMember'
import { useEffect, useState } from 'react'

interface IProps {
  memberId: string
  open: boolean
}

export const MemberPackage = ({ memberId, open = false }: IProps) => {
  const { getMemberPackages, isLoading } = useMember()
  const [packages, setPackages] = useState<IMemberSubscriptionPlanResponse>()
  const [currentPageIndex, setCurrentPageIndex] = useState(1)
  const [currentPageSize, setCurrentPageSize] = useState(10)
  useEffect(() => {
    if (!open) return
    loadData(currentPageIndex, currentPageSize)
  }, [open])
  const servicePlanColumns: ColumnsType<IMemberSubscriptionPlan> = [
    {
      title: 'STT',
      align: 'center',
      dataIndex: 'index',
      key: 'index',
      width: 50,
      render: (record, value, index: number) => index + 1
    },
    {
      title: 'Mã gói dịch vụ',
      align: 'center',
      dataIndex: 'packageDetails',
      key: 'packageDetails',
      width: '10%',
      render: (record) => record?.code
    },
    {
      title: 'Tên gói',
      align: 'center',
      dataIndex: 'packageDetails',
      key: 'packageDetails',
      width: '20%',
      render: (record) => record?.name
    },

    {
      title: 'Số lượng cấu hình tối đa',
      align: 'center',
      dataIndex: 'initialConfigLimit',
      key: 'initialConfigLimit'
    },

    {
      title: 'Cấu hình đã sử dụng',
      align: 'center',
      dataIndex: 'currentConfig',
      key: 'currentConfig'
    },

    {
      title: 'Số lượng giao dịch tối đa',
      align: 'center',
      dataIndex: 'initialTransactionLimit',
      key: 'initialTransactionLimit'
    },
    {
      title: 'Giao dịch đã sử dụng',
      align: 'center',
      dataIndex: 'currentTransaction',
      key: 'currentTransaction'
    },

    {
      title: 'Ngày kích hoạt',
      align: 'center',
      dataIndex: 'createdDate',
      key: 'createdDate',
      render: (value) => moment(value).format('DD/MM/YYYY')
    },
    {
      title: 'Ngày hết hạn',
      align: 'center',
      dataIndex: 'expiredDate',
      key: 'expiredDate',
      render: (value) => (value ? moment(value).format('DD/MM/YYYY') : '')
    },
    {
      title: 'Trạng thái',
      align: 'center',
      dataIndex: 'status',
      key: 'status',
      render: (value) => <Tag color={enumData.MEMBER_PACKAGE_STATUS[value]?.color}>{enumData.MEMBER_PACKAGE_STATUS[value]?.value}</Tag>
    }
  ]

  const loadData = (pageIndex?: number, pageSize?: number) => {
    setCurrentPageIndex(pageIndex || 1)
    setCurrentPageSize(pageSize || 10)

    getMemberPackages({ id: memberId, pageIndex, pageSize }).then((res) => {
      setPackages(res)
    })
  }

  return (
    <Card>
      <BaseTable data={packages?.data} columns={servicePlanColumns} total={packages?.total} isLoading={isLoading} onPageChange={loadData} />
    </Card>
  )
}
