import { <PERSON>, Col, Row, Tabs, Tag } from 'antd'
import { IMember } from '~/dto/member.dto'
import { enumData } from '~/common/enums/enumData'
import { useState } from 'react'
import { MemberOrder } from './member-order'
import { MemberConfig } from './member-config'
import { MemberPackage } from './member-package'
import { MemberKey } from './member-key'

export const MemberDetail = ({ data }: { data: IMember }) => {
  const [currentTab, setCurrentTab] = useState('1')

  const onTabChange = (key: string) => {
    setCurrentTab(key)
  }

  const MemberDetail = () => {
    return (
      <Card>
        <Row>
          <Col span={12}>
            <Col span={24} style={{ margin: 20 }}>
              <span style={{ fontWeight: 'bold', fontSize: 20 }}>Họ Tên: </span>
              <span style={{ fontSize: 16 }}>{data.fullName}</span>
            </Col>

            <Col span={24} style={{ margin: 20 }}>
              <span style={{ fontWeight: 'bold', fontSize: 20 }}>Email: </span>
              <span style={{ fontSize: 16 }}>{data.email}</span>
            </Col>

            <Col span={24} style={{ margin: 20 }}>
              <span style={{ fontWeight: 'bold', fontSize: 20 }}>Trạng thái: </span>
              <Tag style={{ fontSize: 16, padding: 5 }} color={enumData.MEMBER_ACTIVE_STATUS[data.status]?.color}>
                {enumData.MEMBER_ACTIVE_STATUS[data.status]?.value}
              </Tag>
            </Col>
          </Col>

          <Col span={12}>
            <Col span={24} style={{ margin: 10 }}>
              <div style={{ fontWeight: 'bold', fontSize: 20 }}>Ảnh đại diện: </div>
              {data.avatar && <img src={data.avatar} alt='avatar' style={{ width: 100, height: 100, objectFit: 'cover' }} />}
            </Col>
          </Col>
        </Row>
      </Card>
    )
  }

  return (
    <div style={{ width: '100%' }}>
      <Tabs defaultActiveKey='1' onChange={(key) => onTabChange(key)}>
        <Tabs.TabPane tab='Thông tin khách hàng' key='1'>
          <MemberDetail />
        </Tabs.TabPane>

        <Tabs.TabPane tab='Danh sách đơn hàng' key='2'>
          <MemberOrder memberId={data.id} open={currentTab == '2'} />
        </Tabs.TabPane>

        <Tabs.TabPane tab='Danh sách cấu hình' key='3'>
          <MemberConfig memberId={data.id} open={currentTab == '3'} />
        </Tabs.TabPane>

        <Tabs.TabPane tab='Gói đăng ký' key='4'>
          <MemberPackage memberId={data.id} open={currentTab == '4'} />
        </Tabs.TabPane>

        <Tabs.TabPane tab='Thông tin khóa bảo mật' key='5'>
          <MemberKey memberId={data.id} open={currentTab == '5'} />
        </Tabs.TabPane>
      </Tabs>
    </div>
  )
}
