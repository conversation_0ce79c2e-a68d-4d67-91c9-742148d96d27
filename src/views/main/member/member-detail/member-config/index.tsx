import { ApiOutlined, DatabaseOutlined, CodeOutlined, InfoCircleFilled, CopyOutlined } from '@ant-design/icons'
import { Button, Card, Col, Tabs, Tag, Typography } from 'antd'
import { ColumnsType } from 'antd/es/table'
import moment from 'moment'
import { enumData } from '~/common/enums/enumData'
import BaseTable from '~/components/BaseTable'
import { ILogs, IMemberConfigPackage, IMemberConfigPackageResponse } from '~/dto/subscriptionPlan.dto'
import { useMember } from '../../Hooks/useMember'
import { useEffect, useState } from 'react'

interface IProps {
  memberId: string
  open: boolean
}
const { Text, Paragraph } = Typography

export const MemberConfig = ({ memberId, open = false }: IProps) => {
  const { getMemberConfigLogs, getMemberConfigPackages, getMemberConfigPackageDetails, isLoading } = useMember()
  const [configPackages, setConfigPackages] = useState<IMemberConfigPackageResponse>()
  const [expandRowKeys, setExpandRowKeys] = useState<string[]>([])

  //Lưu lại pageIndex, pageSize hiện tại để không bị reset khi chuyển tab
  const [currentPageIndex, setCurrentPageIndex] = useState(1)
  const [currentPageSize, setCurrentPageSize] = useState(10)

  useEffect(() => {
    if (!open) {
      setExpandRowKeys([])
      return
    }
    loadData(currentPageIndex, currentPageSize)
  }, [open])

  //Lấy danh sách gói cấu hình (găn key cho từng cấu hình để fix lỗi EXPAND table ANTD)
  const loadData = async (pageIndex?: number, pageSize?: number) => {
    setCurrentPageIndex(pageIndex || 1)
    setCurrentPageSize(pageSize || 10)
    getMemberConfigPackages({ id: memberId, pageIndex, pageSize }).then((res) => {
      res.data.forEach((value: any) => {
        value.key = value.id
      })
      setConfigPackages(res)
    })
  }

  const loadLogs = async (record, pageIndex?: number, pageSize?: number) => {
    record.logs = await getMemberConfigLogs({ configId: record.id, pageIndex, pageSize })
  }

  const loadDetailConfigPackage = async (id: string) => {
    return await getMemberConfigPackageDetails(id)
  }

  //Col gói cấu hình
  const configPackageColumns: ColumnsType<IMemberConfigPackage> = [
    {
      title: 'STT',
      align: 'center',
      dataIndex: 'index',
      key: 'index',
      width: 50,
      render: (text, record, index) => index + 1
    },
    {
      title: 'Mã gói cấu hình',
      align: 'center',
      dataIndex: 'code',
      key: 'code'
    },

    //name
    {
      title: 'Tên gói cấu hình',
      align: 'center',
      dataIndex: 'name',
      key: 'name'
    },

    //ngày mua
    {
      title: 'Ngày đặt hàng',
      align: 'center',
      dataIndex: 'createdDate',
      key: 'createdDate',
      render: (value) => (value ? moment(value).format('DD/MM/YYYY') : '')
    },

    {
      title: 'Trạng thái',
      align: 'center',
      dataIndex: 'status',
      key: 'status',
      render: (value) => <Tag color={enumData.CONFIG_STATUS[value]?.color}>{enumData.CONFIG_STATUS[value]?.value}</Tag>
    }
  ]

  // Col logs
  const detailConfigPackageColumns: ColumnsType<ILogs> = [
    {
      title: 'STT',
      align: 'center',
      dataIndex: 'index',
      key: 'index',
      width: 50,
      render: (text, record, index) => index + 1
    },
    //thời gian
    {
      title: 'Thời gian',
      align: 'center',
      dataIndex: 'createdDate',
      key: 'createdDate',
      render: (value) => (value ? moment(value).format('DD/MM/YYYY') : '')
    },
    //nội dung
    {
      title: 'Nội dung',
      align: 'center',
      render: (record) => (
        <Tag color='blue' style={{ fontSize: 14 }}>
          {record?.host + record?.url}{' '}
        </Tag>
      )
    },
    //method
    {
      title: 'Phương thức',
      align: 'center',
      dataIndex: 'method',
      key: 'method',
      render: (value) => (
        <Tag color='orange' style={{ fontSize: 14 }}>
          {value}
        </Tag>
      )
    },
    //status
    {
      title: 'Trạng thái',
      align: 'center',
      dataIndex: 'statusCode',
      key: 'statusCode'
    },
    //isTest
    {
      title: 'Là test',
      align: 'center',
      dataIndex: 'isTest',
      key: 'isTest',
      render: (value) => (value ? 'Có' : 'Không')
    }
  ]

  const handleCopyText = (text: string): void => {
    navigator.clipboard.writeText(text)
  }

  const handleExpand = async (expanded: boolean, record: IMemberConfigPackage) => {
    if (!expanded) {
      setExpandRowKeys(expandRowKeys.filter((key) => key !== record.id))
      return
    }
    if (expanded) {
      setExpandRowKeys([...expandRowKeys, record.id])
      record.logs = await getMemberConfigLogs({ configId: record.id })
      record.details = await loadDetailConfigPackage(record.id)
    }
  }
  return (
    <Card>
      <BaseTable
        data={configPackages?.data}
        columns={configPackageColumns}
        total={configPackages?.total}
        isLoading={isLoading}
        onPageChange={loadData}
        expandable={{
          onExpand: handleExpand,
          expandedRowKeys: expandRowKeys,
          expandedRowRender: (record) => {
            return (
              record?.logs && (
                <Tabs>
                  <Tabs.TabPane tab='API Config' key='1'>
                    <Card
                      title={
                        <>
                          <ApiOutlined /> Thông tin API
                        </>
                      }>
                      <Col span={24} style={{ display: 'flex', alignItems: 'center', marginBottom: 20 }}>
                        <b style={{ fontSize: 16, marginRight: 5 }}>Method:</b>{' '}
                        <Tag color='blue' style={{ fontSize: 14 }}>
                          {record?.details?.apiInfo?.method}
                        </Tag>
                      </Col>

                      <Col span={24} style={{ display: 'flex', alignItems: 'center', marginBottom: 20 }}>
                        <b style={{ fontSize: 16, marginRight: 5 }}>Host:</b> <Text code>{record?.details?.apiInfo?.host}</Text>
                      </Col>
                      <Col span={24} style={{ display: 'flex', alignItems: 'center', marginBottom: 20 }}>
                        <b style={{ fontSize: 16, marginRight: 5 }}>Path:</b> <Text code>{record?.details?.apiInfo?.path}</Text>
                      </Col>
                      <Col span={24} style={{ display: 'flex', alignItems: 'center', marginBottom: 20 }}>
                        <b style={{ fontSize: 16, marginRight: 5 }}>Full URL:</b>{' '}
                        <Text copyable={{ text: record?.details?.apiInfo?.host + record?.details?.apiInfo?.path }}>
                          <Tag color='blue' style={{ fontSize: 14 }}>
                            {record?.details?.apiInfo?.host + record?.details?.apiInfo?.path}
                          </Tag>
                        </Text>
                      </Col>
                      <Col span={24} style={{ display: 'flex', alignItems: 'center', marginBottom: 20 }}>
                        <b style={{ fontSize: 16, marginRight: 5 }}>Config ID:</b>{' '}
                        <Text copyable={{ text: record?.details?.apiInfo?.configId }}>
                          <Tag color='blue' style={{ fontSize: 14 }}>
                            {record?.details?.apiInfo?.configId}
                          </Tag>
                        </Text>
                      </Col>
                    </Card>
                    <Card
                      title={
                        <>
                          <DatabaseOutlined /> Request Body
                        </>
                      }>
                      <div style={{ backgroundColor: '#f5f5f5', padding: 12, borderRadius: 6 }}>
                        <Paragraph>
                          <pre style={{ margin: 0, fontSize: 12 }}>{JSON.stringify(record?.details?.apiInfo?.body, null, 2)}</pre>
                        </Paragraph>
                        <Button
                          type='text'
                          size='small'
                          icon={<CopyOutlined />}
                          onClick={() => handleCopyText(JSON.stringify(record?.details?.apiInfo?.body, null, 2))}
                          style={{ marginTop: 8 }}>
                          Copy JSON
                        </Button>
                      </div>
                    </Card>
                    <Card
                      title={
                        <>
                          <CodeOutlined /> Curl Command
                        </>
                      }>
                      <div style={{ backgroundColor: '#1f1f1f', padding: 12, borderRadius: 6 }}>
                        <Paragraph>
                          <pre
                            style={{
                              margin: 0,
                              color: '#fff',
                              fontSize: 12,
                              whiteSpace: 'pre-wrap'
                            }}>
                            {record?.details?.apiInfo?.curlText}
                          </pre>
                        </Paragraph>
                        <Button
                          type='primary'
                          size='small'
                          icon={<CopyOutlined />}
                          onClick={() => handleCopyText(record?.details?.apiInfo?.curlText)}
                          style={{ marginTop: 8 }}>
                          Copy Curl
                        </Button>{' '}
                      </div>
                    </Card>
                    <Card
                      title={
                        <>
                          <InfoCircleFilled /> Thông tin metadata
                        </>
                      }>
                      <div>
                        <Text strong>Ngày tạo:</Text>
                        <Text> {record?.details?.createdDate && moment(record?.details?.createdDate).format('DD/MM/YYYY')}</Text>
                      </div>
                      <div>
                        <Text strong>Ngày cập nhật:</Text>
                        <Text> {record?.details?.updatedDate ? moment(record?.details?.updatedDate).format('DD/MM/YYYY') : ''}</Text>
                      </div>
                    </Card>
                  </Tabs.TabPane>
                  <Tabs.TabPane tab='Logs' key='2'>
                    <BaseTable
                      data={record?.logs?.data}
                      columns={detailConfigPackageColumns}
                      total={record?.logs?.total}
                      isLoading={isLoading}
                      scroll={{ x: 'max-content' }}
                      onPageChange={(pageIndex, pageSize) => loadLogs(record, pageIndex, pageSize)}
                    />
                  </Tabs.TabPane>
                </Tabs>
              )
            )
          },

          rowExpandable: (record) => record?.name !== 'Not Expandable'
        }}
      />
    </Card>
  )
}
