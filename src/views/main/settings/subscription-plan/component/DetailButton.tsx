import { EyeOutlined } from '@ant-design/icons'
import BaseButton from '~/components/BaseButton'
import { ISubscriptionPlan } from '~/dto/subscriptionPlan.dto'
import BaseModal from '~/components/BaseModal'
import { Card, Col, Row, Typography } from 'antd'
import { useModal } from '~/views/global-hooks/useModal'

interface DetailButtonProps {
  data: ISubscriptionPlan
}

const { Text } = Typography
const DetailButton = ({ data }: DetailButtonProps) => {
  const { open, openModal, closeModal } = useModal()

  const modalContent = (
    <Card>
      <Row gutter={24}>
        <Col span={8} style={{ marginBottom: 15 }}>
          <Text strong>Mã gói:</Text>
          <p>{data.code}</p>
        </Col>
        <Col span={8} style={{ marginBottom: 15 }}>
          <Text strong>Tên gói:</Text>
          <p>{data.name}</p>
        </Col>

        <Col span={8} style={{ marginBottom: 15 }}>
          <Text strong>Số lượng giao dịch:</Text>
          <p>{data.transactionLimit}</p>
        </Col>
        <Col span={8} style={{ marginBottom: 15 }}>
          <Text strong>Giá gốc (tháng):</Text>
          <p>{data.originalPrice}</p>
        </Col>
        <Col span={8} style={{ marginBottom: 15 }}>
          <Text strong>Giá bán (tháng):</Text>
          <p>{data.sellPriceMonthly}</p>
        </Col>
        <Col span={8} style={{ marginBottom: 15 }}>
          <Text strong>Giá bán (năm):</Text>
          <p>{data.sellPriceYearly}</p>
        </Col>
        <Col span={24}>
          <Text strong>Mô tả:</Text>
          <div
            style={{
              backgroundColor: '#F5F5F5',
              border: '1px solid #D9D9D9',
              borderRadius: 8,
              color: '#595959',
              fontSize: 15,
              padding: 10,
              marginTop: 10
            }}>
            {data.description}
          </div>
        </Col>
      </Row>
    </Card>
  )

  return (
    <>
      <BaseButton icon={<EyeOutlined />} onClick={openModal} type='primary' />
      <BaseModal open={open} onClose={closeModal} title='Chi tiết gói dịch vụ' childrenBody={modalContent}></BaseModal>
    </>
  )
}

export default DetailButton
