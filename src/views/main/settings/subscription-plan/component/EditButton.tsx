import { EditOutlined } from '@ant-design/icons'
import { useForm } from 'antd/es/form/Form'
import { useEffect, useState } from 'react'
import { useTranslation } from 'react-i18next'
import { BaseButton } from '~/components'
import BaseModal from '~/components/BaseModal'
import { ISubscriptionPlan } from '~/dto/subscriptionPlan.dto'
import { CreateOrEditSubscriptionPlanView } from './CreateOrEditSubscriptionPlanView'
import { useModal } from '~/views/global-hooks/useModal'
import { Tooltip } from 'antd'

interface EditButtonProps {
  data: ISubscriptionPlan
  onSuccess?: () => void
  onClose?: () => void
}

const EditButton = ({ data, onClose }: EditButtonProps) => {
  const [open, setOpen] = useState(false)
  const openModal = () => {
    setOpen(true)
  }

  const closeModal = () => {
    setOpen(false)
    onClose && onClose()
  }

  return (
    <>
      <BaseButton
        icon={<EditOutlined />}
        onClick={openModal}
        type='primary'
        tooltip={
          'Nếu là gói dùng thử thì tick chọn checkbox "Dùng thử"  không cần nhập giá (hệ thống tự điền = 0) Nếu không phải là gói dùng thử phải nhập 1 trong 2 giá bán (tháng/năm)'
        }
      />
      <CreateOrEditSubscriptionPlanView data={data} open={open} onClose={closeModal} />
    </>
  )
}

export default EditButton
