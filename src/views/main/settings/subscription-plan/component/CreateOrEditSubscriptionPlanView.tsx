import { SaveOutlined } from '@ant-design/icons'
import { Button, Card, Checkbox, Col, Form, Input, InputNumber, Row, Select, Spin } from 'antd'
import { useForm } from 'antd/es/form/Form'
import TextArea from 'antd/es/input/TextArea'
import { FC, useEffect, useState } from 'react'
import { enumData } from '~/common/enums/enumData'
import BaseModal from '~/components/BaseModal'
import { ISubscriptionPlan } from '~/dto/subscriptionPlan.dto'
import { useSubscriptionPlan } from '../Hooks/useSubscriptionPlan'

interface IProps {
  data?: ISubscriptionPlan
  open: boolean
  onClose: () => void
}
export const CreateOrEditSubscriptionPlanView: FC<IProps> = ({ data, open, onClose }: IProps) => {
  const [form] = useForm<ISubscriptionPlan>()
  const { loadData, useUpdate, useCreate } = useSubscriptionPlan()
  const { mutateAsync: updateSubscriptionPlan, isPending: isUpdating } = useUpdate()
  const { mutateAsync: createSubscriptionPlan, isPending: isCreating } = useCreate()
  const [isTrial, setIsTrial] = useState(false)

  useEffect(() => {
    if (data && open) {
      form.setFieldsValue({
        ...data
      })
      setIsTrial(data.isTrial)
    } else {
      form.resetFields()
    }
  }, [open, data, form])

  const handleMonthlyPrice = (value: number) => {
    form.setFieldValue('sellPriceYearly', value * 12)
  }

  const handleSave = async (values: any) => {
    if (data) {
      const formattedValues = {
        ...data,
        ...values,
        originalPrice: Number(values.originalPrice) || 0,
        sellPriceMonthly: Number(values.sellPriceMonthly) || 0,
        sellPriceYearly: Number(values.sellPriceYearly) || 0,
        configLimitYearly: Number(values.configLimitYearly) || 0,
        transactionLimitYearly: Number(values.transactionLimitYearly) || 0,
        transactionLimit: Number(values.transactionLimit) || 0,
        configLimit: Number(values.configLimit) || 0,
        byteLimit: Number(values.byteLimit) || 0,
        projectLimit: Number(values.projectLimit) || 0,
        byteLimitYearly: Number(values.byteLimitYearly) || 0,
        projectLimitYearly: Number(values.projectLimitYearly) || 0
      }
      await updateSubscriptionPlan(formattedValues)
    } else {
      const formattedValues = {
        ...values,
        originalPrice: Number(values.originalPrice) || 0,
        sellPriceMonthly: Number(values.sellPriceMonthly) || 0,
        sellPriceYearly: Number(values.sellPriceYearly) || 0,
        configLimitYearly: Number(values.configLimitYearly) || 0,
        transactionLimitYearly: Number(values.transactionLimitYearly) || 0,
        transactionLimit: Number(values.transactionLimit) || 0,
        configLimit: Number(values.configLimit) || 0,
        byteLimit: Number(values.byteLimit) || 0,
        projectLimit: Number(values.projectLimit) || 0,
        byteLimitYearly: Number(values.byteLimitYearly) || 0,
        projectLimitYearly: Number(values.projectLimitYearly) || 0
      }
      await createSubscriptionPlan(formattedValues).then(() => {
        handleClose()
      })
    }
  }
  const handleClose = () => {
    onClose()
    form.resetFields()
  }
  const handleIsTrial = (checked: boolean) => {
    setIsTrial(checked)
    form.setFieldValue('originalPrice', 0)
    form.setFieldValue('sellPriceYearly', 0)
    form.setFieldValue('sellPriceMonthly', 0)
  }
  const modalContent = (isTrial: boolean) => (
    <Spin spinning={data ? isUpdating : isCreating}>
      <Form form={form} layout='vertical' onFinish={handleSave}>
        <Row gutter={24}>
          <Col span={6}>
            <Form.Item label='Tên gói' name='name' rules={[{ required: true, message: 'Vui lòng nhập tên gói!' }]}>
              <Input placeholder='Nhập tên gói' />
            </Form.Item>
          </Col>
          <Col span={6}>
            <Form.Item label='Giá gốc' name='originalPrice'>
              <InputNumber min={0} style={{ width: '100%' }} disabled={isTrial} placeholder='Nhập giá gốc' />
            </Form.Item>
          </Col>

          <Col span={6}>
            <Form.Item label='Gói dùng thử' name='isTrial' valuePropName='checked'>
              <Checkbox onChange={(e) => handleIsTrial(e.target.checked)}></Checkbox>
            </Form.Item>
          </Col>
        </Row>
        <Card title='Tháng'>
          <Row gutter={24}>
            <Col span={8}>
              <Form.Item label='Giá bán (tháng)' name='sellPriceMonthly'>
                <InputNumber
                  style={{ width: '100%' }}
                  disabled={isTrial}
                  min={0}
                  onChange={(e) => handleMonthlyPrice(Number(e))}
                  type='number'
                  placeholder='Nhập giá bán'
                />
              </Form.Item>
            </Col>

            <Col span={8}>
              <Form.Item label='Giới hạn cấu hình (tháng)' name='configLimit'>
                <InputNumber style={{ width: '100%' }} min={0} type='number' placeholder='Nhập số lượng cấu hình theo tháng' />
              </Form.Item>
            </Col>

            <Col span={8}>
              <Form.Item label='Giới hạn giao dịch (tháng)' name='transactionLimit'>
                <InputNumber style={{ width: '100%' }} min={0} type='number' placeholder='Nhập số lượng giao dịch theo tháng' />
              </Form.Item>
            </Col>
          </Row>
          <Row gutter={24}>
            <Col span={8}>
              <Form.Item label='Giới hạn dung lượng (Byte) (tháng)' name='byteLimit'>
                <InputNumber min={0} style={{ width: '100%' }} placeholder='Nhập giới hạn dung lượng (Byte)' />
              </Form.Item>
            </Col>

            <Col span={8}>
              <Form.Item label='Giới hạn project (tháng)' name='projectLimit'>
                <InputNumber min={0} style={{ width: '100%' }} placeholder='Nhập giới hạn project' />
              </Form.Item>
            </Col>
          </Row>
          <Col span={24}>
            <Form.Item label='Mô tả gói tháng' name='description'>
              <TextArea rows={4} placeholder='Nhập mô tả gói tháng' />
            </Form.Item>
          </Col>
        </Card>

        <Card title='Năm'>
          <Row gutter={24}>
            <Col span={8}>
              <Form.Item label='Giá bán (năm)' name='sellPriceYearly'>
                <InputNumber disabled={isTrial} style={{ width: '100%' }} min={0} type='number' placeholder='Nhập giá bán theo năm' />
              </Form.Item>
            </Col>

            <Col span={8}>
              <Form.Item label='Giới hạn cấu hình (năm)' name='configLimitYearly'>
                <InputNumber style={{ width: '100%' }} min={0} type='number' placeholder='Nhập số lượng cấu hình theo năm' />
              </Form.Item>
            </Col>

            <Col span={8}>
              <Form.Item label='Giới hạn giao dịch (năm)' name='transactionLimitYearly'>
                <InputNumber style={{ width: '100%' }} min={0} type='number' placeholder='Nhập số lượng giao dịch theo năm' />
              </Form.Item>
            </Col>
          </Row>
          <Row gutter={24}>
            <Col span={8}>
              <Form.Item label='Giới hạn dung lượng (Byte) (năm)' name='byteLimitYearly'>
                <InputNumber min={0} style={{ width: '100%' }} placeholder='Nhập giới hạn dung lượng (Byte)' />
              </Form.Item>
            </Col>

            <Col span={8}>
              <Form.Item label='Giới hạn project (năm)' name='projectLimitYearly'>
                <InputNumber min={0} style={{ width: '100%' }} placeholder='Nhập giới hạn project' />
              </Form.Item>
            </Col>
          </Row>
        </Card>
        <Col span={24}>
          <Form.Item label='Mô tả gói năm' name='descriptionYearly'>
            <TextArea rows={4} placeholder='Nhập mô tả gói năm' />
          </Form.Item>
        </Col>
        <Row
          style={{
            marginTop: 24,
            borderTop: '1px solid #f0f0f0',
            paddingTop: 16
          }}
          justify={'center'}>
          <Button onClick={handleClose} style={{ marginRight: 8 }}>
            Hủy
          </Button>
          <Button type='primary' htmlType='submit' icon={<SaveOutlined />}>
            {data ? 'Cập nhật' : 'Tạo mới'}
          </Button>
        </Row>
      </Form>
    </Spin>
  )
  return (
    <BaseModal open={open} onClose={handleClose} title={data ? 'Chỉnh sửa gói dịch vụ' : 'Tạo gói dịch vụ'} childrenBody={modalContent(isTrial)} />
  )
}
