import { useMutation, useQuery } from '@tanstack/react-query'
import { apiDocumentService } from '~/services/ApiDocumentService/apiDocumentContentService'

import {
  IApiDocContentCreate,
  IApiDocContentResponse,
  IApiDocContentUpdate,
} from '~/dto/apiDocumentContent'
import { toastService } from '~/services'

export const useApiDocument = (enabled = false) => {
  const {
    data: response,
    isFetching,
    refetch,
  } = useQuery<IApiDocContentResponse>({
    queryKey: [apiDocumentService.APIs.LIST],
    queryFn: () => apiDocumentService.getApiDocumentList(),
    enabled,
  })

  const createMutation = useMutation({
    mutationFn: (data: IApiDocContentCreate) =>
      apiDocumentService.createApiDocument(data),
    onSuccess: () => {
      toastService.success('Tạo tài liệu API thành công')
      refetch()
    },
    onError: (error: any) => {
      toastService.error(error?.message || 'Có lỗi xảy ra khi tạo tài liệu API')
    },
  })

  const updateMutation = useMutation({
    mutationFn: (data: IApiDocContentUpdate) =>
      apiDocumentService.updateApiDocument(data),
    onSuccess: () => {
      toastService.success('Cập nhật tài liệu API thành công')
    },
    onError: (error: any) => {
      toastService.error(error?.message || 'Có lỗi xảy ra khi cập nhật tài liệu API')
    },
  })

  return {
    data: response?.data ?? [],
    total: response?.total ?? 0,
    isFetching,
    refetch,
    createMutation,
    updateMutation,
  }
}
