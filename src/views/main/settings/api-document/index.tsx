import BaseView from '~/components/BaseView'
import { useApiDocument } from './Hooks/useApiDocument'
import BaseTable from '~/components/BaseTable'
import moment from 'moment'
import { Outlet, useNavigate } from 'react-router-dom'
import { Button } from 'antd'

export const ApiDocumentView = () => {
  const { data, total, isFetching, refetch } = useApiDocument(true)
  const navigate = useNavigate()

  const columns = [
    {
      title: 'Tiêu đề',
      dataIndex: 'title',
      key: 'title'
    },
    {
      title: 'Slug',
      dataIndex: 'slug',
      key: 'slug'
    },
    // content
    {
      title: 'Nội dung',
      dataIndex: 'content',
      key: 'content'
    },
    {
      title: 'Ng<PERSON>y tạo',
      dataIndex: 'createdDate',
      key: 'createdDate',

      render: (value) => (value ? moment(value).format('DD/MM/YYYY HH:mm:ss') : '')
    },
    {
      title: '<PERSON><PERSON><PERSON> cập nhật',
      dataIndex: 'updatedDate',
      key: 'updatedDate',

      render: (value) => (value ? moment(value).format('DD/MM/YYYY HH:mm:ss') : '')
    }
  ]

  const handleNavigateCreate = () => {
    navigate('/settings/setting-documents/create')
  }

  return (
    <BaseView>
      <Button type='primary' onClick={handleNavigateCreate}>
        Create
      </Button>
      <BaseTable columns={columns} data={data} total={total} isLoading={isFetching} />
    </BaseView>
  )
}
