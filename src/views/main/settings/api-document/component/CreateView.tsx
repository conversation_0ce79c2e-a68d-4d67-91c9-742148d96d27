import BaseView from '~/components/BaseView'
import { <PERSON><PERSON>, Card, Col, Row, Form, Input, Spin } from 'antd'
import { EyeOutlined, SaveOutlined } from '@ant-design/icons'
import { useApiDocument } from '../Hooks/useApiDocument'
import ReactQuill from 'react-quill'
import { useEffect, useState } from 'react'
import { IApiDocContentCreate } from '~/dto/apiDocumentContent'
import { useForm } from 'antd/es/form/Form'

export const CreateView = () => {
  const { createMutation } = useApiDocument()
  const [form] = useForm<IApiDocContentCreate>()
  const [previewData, setPreviewData] = useState<{
    title: string
    slug: string
    content: string
  } | null>(null)
  const [quillRef, setQuillRef] = useState<any>(null)

  const handleSave = async (values: IApiDocContentCreate) => {
    try {
      await createMutation.mutateAsync(values)
      form.resetFields()
      setPreviewData(null)
    } catch (error) {
      console.error('Error creating document:', error)
    }
  }

  const handleView = () => {
    const formValues = form.getFieldsValue()
    setPreviewData({
      title: formValues.title || '',
      slug: formValues.slug || '',
      content: formValues.content || ''
    })
  }

  const handleImageUpload = async (quill: any) => {
    const input = document.createElement('input')
    input.setAttribute('type', 'file')
    input.setAttribute('accept', 'image/*')
    input.click()

    console.log('quill', quill)

    // input.onchange = async () => {
    //   const file = input.files?.[0]
    //   if (!file) return

    //   try {
    //     const formData = new FormData()
    //     formData.append('file', file)

    //     // Gọi API backend của bạn để upload ảnh lên S3
    //     const res = await fetch('/api/upload-to-s3', {
    //       method: 'POST',
    //       body: formData
    //     })

    //     const { imageUrl } = await res.json()

    //     // Insert ảnh vào vị trí con trỏ hiện tại
    //     const range = quill.getSelection()
    //     quill.insertEmbed(range.index, 'image', imageUrl)
    //   } catch (error) {
    //     console.error('Upload error:', error)
    //   }
    // }
  }

  const modules = {
    toolbar: {
      container: [[{ header: [1, 2, false] }], ['bold', 'italic', 'underline'], ['link', 'image'], ['clean']],
      handlers: {
        image: handleImageUpload
      }
    }
  }

  useEffect(() => {
    if (!quillRef) return
    const quill = quillRef.getEditor()

    // Override handler "image"
    const toolbar = quill.getModule('toolbar')
    toolbar.addHandler('image', () => handleImageUpload(quill))
  }, [quillRef])

  return (
    <BaseView>
      <Card title='Tạo mới tài liệu'>
        <Spin spinning={createMutation.isPending}>
          <Form form={form} layout='vertical' onFinish={handleSave}>
            <Row gutter={24}>
              {/* Title */}
              <Col span={12}>
                <Form.Item label='Tiêu đề' name='title' rules={[{ required: true, message: 'Vui lòng nhập tiêu đề!' }]}>
                  <Input placeholder='Nhập tiêu đề tài liệu' />
                </Form.Item>
              </Col>
              {/* Slug */}
              <Col span={12}>
                <Form.Item label='Slug' name='slug'>
                  <Input placeholder='Nhập slug (tùy chọn)' />
                </Form.Item>
              </Col>
            </Row>
            <Row gutter={24}>
              <Col span={24}>
                <Form.Item label='Nội dung' name='content' rules={[{ required: true, message: 'Vui lòng nhập nội dung!' }]}>
                  <ReactQuill ref={quillRef} theme='snow' style={{ height: '300px' }} modules={modules} placeholder='Nhập nội dung tài liệu...' />
                </Form.Item>
              </Col>
            </Row>
            <Row style={{ marginTop: 60 }}>
              <Col span={12} style={{ textAlign: 'left', display: 'flex', gap: 8 }}>
                <Button type='primary' htmlType='submit' icon={<SaveOutlined />}>
                  Save
                </Button>
                <Button type='default' icon={<EyeOutlined />} onClick={handleView}>
                  View
                </Button>
              </Col>
            </Row>
          </Form>
        </Spin>
      </Card>
      <Card title='Xem trước tài liệu' style={{ marginTop: 16 }}>
        {previewData ? (
          <div>
            <h2 style={{ marginBottom: 16 }}>{previewData.title}</h2>
            {previewData.slug && (
              <p style={{ color: '#666', marginBottom: 16 }}>
                <strong>Slug:</strong> {previewData.slug}
              </p>
            )}
            <div
              dangerouslySetInnerHTML={{ __html: previewData.content }}
              style={{
                border: '1px solid #f0f0f0',
                padding: '16px',
                borderRadius: '6px',
                backgroundColor: '#fafafa'
              }}
            />
          </div>
        ) : (
          <p style={{ color: '#999', textAlign: 'center', padding: '40px' }}>Nhấn nút "View" để xem trước nội dung tài liệu</p>
        )}
      </Card>
    </BaseView>
  )
}
