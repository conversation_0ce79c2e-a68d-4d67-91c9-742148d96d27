import BaseView from '~/components/BaseView'
import { But<PERSON>, Card, Col, Row } from 'antd'
import { EyeOutlined, SaveOutlined } from '@ant-design/icons'
import { useApiDocument } from '../Hooks/useApiDocument'

export const CreateView = () => {
    const { createMutation } = useApiDocument();

  return <BaseView>
    <Card title="Tạo mới tài liệu">
        <Row gutter={24}>
            <Col span={12}>
            </Col>
            <Col span={12}></Col>
        </Row>
        <Row gutter={24}>
            <Col>
            </Col>
        </Row>
        <Row>
            <Col span={12} style={{ textAlign: 'left', display: 'flex', gap: 8 }}>
                <Button type="primary" icon={<SaveOutlined />}>Save</Button>
                <Button type="primary" icon={<EyeOutlined />}>View</Button>
            </Col>
        </Row>
    </Card>
    <Card title="Xem trước tài liệu" style={{ marginTop: 16 }}>

    </Card>
</BaseView>
}
