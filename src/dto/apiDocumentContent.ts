// Entity interface
export interface IApiDocContent {
  id: string;
  slug?: string;
  title: string;
  content: string;
  createdAt?: string;
  updatedAt?: string;
}

// Response interface
export interface IApiDocContentResponse {
  data: IApiDocContent[];
  total: number;
}

// Filter/List request interface
export interface IApiDocContentList {
  page: number;
  limit: number;
  slug?: string;
  title?: string;
}

export interface IApiDocContentCreate {
  slug?: string;
  title: string;
  content: string;
}

export interface IApiDocContentUpdate extends IApiDocContentCreate {
  id: string;
}